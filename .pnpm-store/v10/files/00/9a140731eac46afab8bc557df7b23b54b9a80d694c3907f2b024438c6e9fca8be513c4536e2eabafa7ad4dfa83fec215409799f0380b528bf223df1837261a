"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = intervalToDuration;

var _index = _interopRequireDefault(require("../compareAsc/index.js"));

var _index2 = _interopRequireDefault(require("../differenceInYears/index.js"));

var _index3 = _interopRequireDefault(require("../differenceInMonths/index.js"));

var _index4 = _interopRequireDefault(require("../differenceInDays/index.js"));

var _index5 = _interopRequireDefault(require("../differenceInHours/index.js"));

var _index6 = _interopRequireDefault(require("../differenceInMinutes/index.js"));

var _index7 = _interopRequireDefault(require("../differenceInSeconds/index.js"));

var _index8 = _interopRequireDefault(require("../isValid/index.js"));

var _index9 = _interopRequireDefault(require("../_lib/requiredArgs/index.js"));

var _index10 = _interopRequireDefault(require("../toDate/index.js"));

var _index11 = _interopRequireDefault(require("../sub/index.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/**
 * @name intervalToDuration
 * @category Common Helpers
 * @summary Convert interval to duration
 *
 * @description
 * Convert a interval object to a duration object.
 *
 * @param {Interval} interval - the interval to convert to duration
 *
 * @returns {Duration} The duration Object
 * @throws {TypeError} Requires 2 arguments
 * @throws {RangeError} `start` must not be Invalid Date
 * @throws {RangeError} `end` must not be Invalid Date
 *
 * @example
 * // Get the duration between January 15, 1929 and April 4, 1968.
 * intervalToDuration({
 *   start: new Date(1929, 0, 15, 12, 0, 0),
 *   end: new Date(1968, 3, 4, 19, 5, 0)
 * })
 * // => { years: 39, months: 2, days: 20, hours: 7, minutes: 5, seconds: 0 }
 */
function intervalToDuration(_ref) {
  var start = _ref.start,
      end = _ref.end;
  (0, _index9.default)(1, arguments);
  var dateLeft = (0, _index10.default)(start);
  var dateRight = (0, _index10.default)(end);

  if (!(0, _index8.default)(dateLeft)) {
    throw new RangeError('Start Date is invalid');
  }

  if (!(0, _index8.default)(dateRight)) {
    throw new RangeError('End Date is invalid');
  }

  var duration = {
    years: 0,
    months: 0,
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  };
  var sign = (0, _index.default)(dateLeft, dateRight);
  duration.years = Math.abs((0, _index2.default)(dateLeft, dateRight));
  var remainingMonths = (0, _index11.default)(dateLeft, {
    years: sign * duration.years
  });
  duration.months = Math.abs((0, _index3.default)(remainingMonths, dateRight));
  var remainingDays = (0, _index11.default)(remainingMonths, {
    months: sign * duration.months
  });
  duration.days = Math.abs((0, _index4.default)(remainingDays, dateRight));
  var remainingHours = (0, _index11.default)(remainingDays, {
    days: sign * duration.days
  });
  duration.hours = Math.abs((0, _index5.default)(remainingHours, dateRight));
  var remainingMinutes = (0, _index11.default)(remainingHours, {
    hours: sign * duration.hours
  });
  duration.minutes = Math.abs((0, _index6.default)(remainingMinutes, dateRight));
  var remainingSeconds = (0, _index11.default)(remainingMinutes, {
    minutes: sign * duration.minutes
  });
  duration.seconds = Math.abs((0, _index7.default)(remainingSeconds, dateRight));
  return duration;
}

module.exports = exports.default;