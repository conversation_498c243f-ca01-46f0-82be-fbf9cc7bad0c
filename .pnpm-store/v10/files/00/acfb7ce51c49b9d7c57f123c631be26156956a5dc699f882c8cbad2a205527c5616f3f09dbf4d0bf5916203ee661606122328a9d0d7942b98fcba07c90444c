{"version": 3, "file": "lit-html.d.ts", "sourceRoot": "", "sources": ["../src/lit-html.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,KAAK,EAAC,SAAS,EAA4B,MAAM,gBAAgB,CAAC;AASzE;;;;;GAKG;AAEH,yBAAiB,WAAW,CAAC;IAC3B;;;;;;;;OAQG;IAEH,UAAiB,QAAQ,CAAC;QACxB,KAAY,KAAK,GACb,YAAY,GACZ,oBAAoB,GACpB,8BAA8B,GAC9B,gBAAgB,GAChB,WAAW,GACX,SAAS,GACT,eAAe,GACf,YAAY,CAAC;QACjB,UAAiB,YAAY;YAC3B,IAAI,EAAE,eAAe,CAAC;YACtB,QAAQ,EAAE,QAAQ,CAAC;YACnB,OAAO,EAAE,oBAAoB,CAAC;YAC9B,gBAAgB,EAAE,mBAAmB,CAAC;YACtC,KAAK,EAAE,YAAY,EAAE,CAAC;SACvB;QACD,UAAiB,WAAW;YAC1B,IAAI,EAAE,cAAc,CAAC;YACrB,EAAE,EAAE,MAAM,CAAC;YACX,KAAK,EAAE,OAAO,CAAC;YACf,SAAS,EAAE,WAAW,GAAG,gBAAgB,CAAC;YAC1C,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;YACnC,IAAI,EAAE,SAAS,GAAG,SAAS,CAAC;SAC7B;QACD,UAAiB,SAAS;YACxB,IAAI,EAAE,YAAY,CAAC;YACnB,EAAE,EAAE,MAAM,CAAC;YACX,KAAK,EAAE,OAAO,CAAC;YACf,SAAS,EAAE,WAAW,GAAG,gBAAgB,CAAC;YAC1C,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;YACnC,IAAI,EAAE,SAAS,CAAC;SACjB;QACD,UAAiB,oBAAoB;YACnC,IAAI,EAAE,uBAAuB,CAAC;YAC9B,QAAQ,EAAE,QAAQ,GAAG,gBAAgB,CAAC;YACtC,QAAQ,EAAE,gBAAgB,CAAC;YAC3B,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;YACnC,QAAQ,EAAE,IAAI,CAAC;YACf,KAAK,EAAE,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;YAC/B,MAAM,EAAE,OAAO,EAAE,CAAC;SACnB;QACD,UAAiB,8BAA8B;YAC7C,IAAI,EAAE,mCAAmC,CAAC;YAC1C,QAAQ,EAAE,QAAQ,GAAG,gBAAgB,CAAC;YACtC,QAAQ,EAAE,gBAAgB,CAAC;YAC3B,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;YACnC,QAAQ,EAAE,IAAI,CAAC;YACf,KAAK,EAAE,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;YAC/B,MAAM,EAAE,OAAO,EAAE,CAAC;SACnB;QACD,UAAiB,gBAAgB;YAC/B,IAAI,EAAE,mBAAmB,CAAC;YAC1B,QAAQ,EAAE,QAAQ,GAAG,gBAAgB,CAAC;YACtC,QAAQ,EAAE,gBAAgB,CAAC;YAC3B,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;YACnC,KAAK,EAAE,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;YAC/B,MAAM,EAAE,OAAO,EAAE,CAAC;SACnB;QACD,UAAiB,YAAY;YAC3B,IAAI,EAAE,UAAU,CAAC;YACjB,IAAI,EAAE,IAAI,CAAC;YACX,KAAK,EAAE,OAAO,CAAC;YACf,UAAU,EAAE,MAAM,CAAC;YACnB,MAAM,EAAE,OAAO,EAAE,CAAC;YAClB,gBAAgB,EAAE,gBAAgB,CAAC;SACpC;QAED,KAAY,eAAe,GACvB,yBAAyB,GACzB,UAAU,GACV,UAAU,GACV,eAAe,GACf,cAAc,GACd,sBAAsB,GACtB,mBAAmB,GACnB,sBAAsB,CAAC;QAE3B,UAAiB,yBAAyB;YACxC,IAAI,EAAE,yBAAyB,CAAC;YAChC,KAAK,EAAE,SAAS,CAAC;YACjB,GAAG,EAAE,SAAS,GAAG,IAAI,CAAC;YACtB,MAAM,EAAE,cAAc,GAAG,SAAS,CAAC;YACnC,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;SACpC;QAED,UAAiB,UAAU;YACzB,IAAI,EAAE,aAAa,CAAC;YACpB,IAAI,EAAE,IAAI,CAAC;YACX,KAAK,EAAE,OAAO,CAAC;YACf,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;SACpC;QAED,UAAiB,UAAU;YACzB,IAAI,EAAE,aAAa,CAAC;YACpB,KAAK,EAAE,IAAI,CAAC;YACZ,MAAM,EAAE,cAAc,GAAG,SAAS,CAAC;YACnC,KAAK,EAAE,IAAI,CAAC;YACZ,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;SACpC;QAED,UAAiB,eAAe;YAC9B,IAAI,EAAE,kBAAkB,CAAC;YACzB,OAAO,EAAE,OAAO,CAAC;YACjB,IAAI,EAAE,MAAM,CAAC;YACb,KAAK,EAAE,OAAO,CAAC;YACf,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;SACpC;QAED,UAAiB,cAAc;YAC7B,IAAI,EAAE,iBAAiB,CAAC;YACxB,OAAO,EAAE,OAAO,CAAC;YACjB,IAAI,EAAE,MAAM,CAAC;YACb,KAAK,EAAE,OAAO,CAAC;YACf,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;SACpC;QAED,UAAiB,sBAAsB;YACrC,IAAI,EAAE,0BAA0B,CAAC;YACjC,OAAO,EAAE,OAAO,CAAC;YACjB,IAAI,EAAE,MAAM,CAAC;YACb,KAAK,EAAE,OAAO,CAAC;YACf,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;SACpC;QAED,UAAiB,mBAAmB;YAClC,IAAI,EAAE,uBAAuB,CAAC;YAC9B,OAAO,EAAE,OAAO,CAAC;YACjB,IAAI,EAAE,MAAM,CAAC;YACb,KAAK,EAAE,OAAO,CAAC;YACf,WAAW,EAAE,OAAO,CAAC;YACrB,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;YAEnC,cAAc,EAAE,OAAO,CAAC;YAExB,WAAW,EAAE,OAAO,CAAC;SACtB;QAED,UAAiB,sBAAsB;YACrC,IAAI,EAAE,2BAA2B,CAAC;YAClC,OAAO,EAAE,OAAO,CAAC;YACjB,KAAK,EAAE,OAAO,CAAC;YACf,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;SACpC;KACF;CACF;AA8ED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,oBAAY,gBAAgB,GAAG,CAC7B,IAAI,EAAE,IAAI,EACV,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,UAAU,GAAG,WAAW,KAC3B,cAAc,CAAC;AAEpB;;;;;;;;;;GAUG;AACH,oBAAY,cAAc,GAAG,CAAC,KAAK,EAAE,OAAO,KAAK,OAAO,CAAC;AA+IzD,2BAA2B;AAC3B,QAAA,MAAM,WAAW,IAAI,CAAC;AACtB,QAAA,MAAM,UAAU,IAAI,CAAC;AAErB,aAAK,UAAU,GAAG,OAAO,WAAW,GAAG,OAAO,UAAU,CAAC;AAIzD,QAAA,MAAM,cAAc,IAAI,CAAC;AACzB,QAAA,MAAM,UAAU,IAAI,CAAC;AAIrB,QAAA,MAAM,YAAY,IAAI,CAAC;AACvB,QAAA,MAAM,YAAY,IAAI,CAAC;AAEvB;;;;;;;;;;;;GAYG;AACH,oBAAY,cAAc,CAAC,CAAC,SAAS,UAAU,GAAG,UAAU,IAAI;IAE9D,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;IAClB,OAAO,EAAE,oBAAoB,CAAC;IAC9B,MAAM,EAAE,OAAO,EAAE,CAAC;CACnB,CAAC;AAEF,oBAAY,kBAAkB,GAAG,cAAc,CAAC,OAAO,WAAW,CAAC,CAAC;AAEpE,oBAAY,iBAAiB,GAAG,cAAc,CAAC,OAAO,UAAU,CAAC,CAAC;AAElE,MAAM,WAAW,sBAAsB;IAIrC,CAAC,YAAY,CAAC,EAAE,gBAAgB,CAAC;IACjC,MAAM,EAAE,OAAO,EAAE,CAAC;CACnB;AAED,MAAM,WAAW,gBAAiB,SAAQ,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;IAE5D,EAAE,CAAC,EAAE,mBAAmB,CAAC;IAKzB,CAAC,EAAE,oBAAoB,CAAC;CACzB;AA0BD;;;;;;;;;;;;GAYG;AACH,eAAO,MAAM,IAAI,YA/BL,oBAAoB,aAAa,OAAO,EAAE,sBA+BlB,CAAC;AAErC;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,eAAO,MAAM,GAAG,YAxDJ,oBAAoB,aAAa,OAAO,EAAE,sBAwDpB,CAAC;AAEnC;;;GAGG;AACH,eAAO,MAAM,QAAQ,eAA6B,CAAC;AAEnD;;;;;;;;;;;;;;;;;;GAkBG;AACH,eAAO,MAAM,OAAO,eAA4B,CAAC;AAWjD;;;;;;;GAOG;AACH,MAAM,WAAW,aAAa;IAC5B;;;OAGG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,YAAY,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC;IAChC;;;;OAIG;IACH,aAAa,CAAC,EAAE;QAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,CAAA;KAAC,CAAC;IAC/D;;;;;;;OAOG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;CACvB;AAoBD,MAAM,WAAW,eAAe;IAC9B,QAAQ,CAAC,EAAE,eAAe,CAAC;IAC3B,aAAa,EAAE,OAAO,CAAC;IACvB,WAAW,CAAC,EAAE,SAAS,CAAC;IACxB,YAAY,CAAC,EAAE,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;CAC7C;AA6MD,cAAM,QAAQ;IAIZ,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,CAAM;gBAI9B,EAAC,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE,IAAI,EAAC,EAAE,cAAc,EAC/C,OAAO,CAAC,EAAE,aAAa;IA6JzB,kBAAkB;IAClB,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,aAAa;CAKjE;AAED,MAAM,WAAW,cAAc;IAC7B,QAAQ,CAAC,EAAE,cAAc,CAAC;IAC1B,wBAAwB,CAAC,EAAE,GAAG,CAAC,cAAc,CAAC,CAAC;IAQ/C,aAAa,EAAE,OAAO,CAAC;CACxB;AAED,iBAAS,gBAAgB,CACvB,IAAI,EAAE,SAAS,GAAG,aAAa,GAAG,WAAW,EAC7C,KAAK,EAAE,OAAO,EACd,MAAM,GAAE,eAAsB,EAC9B,cAAc,CAAC,EAAE,MAAM,GACtB,OAAO,CAuCT;AAED,YAAY,EAAC,gBAAgB,EAAC,CAAC;AAC/B;;;GAGG;AACH,cAAM,gBAAiB,YAAW,cAAc;IAC9C,UAAU,EAAE,QAAQ,CAAC;IACrB,OAAO,EAAE,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,CAAM;gBAO1B,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS;IAMjD,IAAI,UAAU,SAEb;IAGD,IAAI,aAAa,YAEhB;IAID,MAAM,CAAC,OAAO,EAAE,aAAa,GAAG,SAAS;IAiDzC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC;CAyB/B;AAKD,aAAK,qBAAqB,GAAG;IAC3B,QAAQ,CAAC,IAAI,EAAE,OAAO,cAAc,CAAC;IACrC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;IACvB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,IAAI,EAAE,OAAO,aAAa,CAAC;IACpC,QAAQ,CAAC,OAAO,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;CACzC,CAAC;AACF,aAAK,iBAAiB,GAAG;IACvB,QAAQ,CAAC,IAAI,EAAE,OAAO,UAAU,CAAC;IACjC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;CACxB,CAAC;AACF,aAAK,mBAAmB,GAAG;IACzB,QAAQ,CAAC,IAAI,EAAE,OAAO,YAAY,CAAC;IACnC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;CACxB,CAAC;AACF,aAAK,mBAAmB,GAAG;IACzB,QAAQ,CAAC,IAAI,EAAE,OAAO,YAAY,CAAC;IACnC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;CACxB,CAAC;AAEF;;;;GAIG;AACH,aAAK,YAAY,GACb,iBAAiB,GACjB,qBAAqB,GACrB,mBAAmB,GACnB,mBAAmB,CAAC;AAExB,oBAAY,IAAI,GACZ,SAAS,GACT,aAAa,GACb,YAAY,GACZ,oBAAoB,GACpB,WAAW,GACX,SAAS,CAAC;AAEd,YAAY,EAAC,SAAS,EAAC,CAAC;AACxB,cAAM,SAAU,YAAW,cAAc;IACvC,QAAQ,CAAC,IAAI,KAAc;IAC3B,QAAQ,CAAC,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;IAC5C,gBAAgB,EAAE,OAAO,CAAW;IAOpC,OAAO,CAAC,cAAc,CAA6B;IAcnD,IAAI,aAAa,YAKhB;gBAgBC,SAAS,EAAE,SAAS,EACpB,OAAO,EAAE,SAAS,GAAG,IAAI,EACzB,MAAM,EAAE,gBAAgB,GAAG,SAAS,GAAG,SAAS,EAChD,OAAO,EAAE,aAAa,GAAG,SAAS;IAgBpC;;;;;;;;;;;;;;;;;OAiBG;IACH,IAAI,UAAU,IAAI,IAAI,CAarB;IAED;;;OAGG;IACH,IAAI,SAAS,IAAI,IAAI,GAAG,IAAI,CAE3B;IAED;;;OAGG;IACH,IAAI,OAAO,IAAI,IAAI,GAAG,IAAI,CAEzB;IAED,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,eAAe,GAAE,eAAsB,GAAG,IAAI;IAsDzE,OAAO,CAAC,OAAO;IAOf,OAAO,CAAC,WAAW;IAyCnB,OAAO,CAAC,WAAW;IAsDnB,OAAO,CAAC,qBAAqB;IAkE7B,OAAO,CAAC,eAAe;CA+FxB;AAED;;;GAGG;AACH,MAAM,WAAW,QAAS,SAAQ,SAAS;IACzC;;;;;;;;;;;;;;OAcG;IACH,YAAY,CAAC,WAAW,EAAE,OAAO,GAAG,IAAI,CAAC;CAC1C;AAED,YAAY,EAAC,aAAa,EAAC,CAAC;AAC5B,cAAM,aAAc,YAAW,cAAc;IAC3C,QAAQ,CAAC,IAAI,gBAIS;IACtB,QAAQ,CAAC,OAAO,EAAE,WAAW,CAAC;IAC9B,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;IAE5C;;;;OAIG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;IAUzC,SAAS,CAAC,UAAU,EAAE,cAAc,GAAG,SAAS,CAAC;IAEjD,IAAI,OAAO,WAEV;IAGD,IAAI,aAAa,YAEhB;gBAGC,OAAO,EAAE,WAAW,EACpB,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE,aAAa,CAAC,MAAM,CAAC,EAC9B,MAAM,EAAE,cAAc,EACtB,OAAO,EAAE,aAAa,GAAG,SAAS;CAqHrC;AAED,YAAY,EAAC,YAAY,EAAC,CAAC;AAC3B,cAAM,YAAa,SAAQ,aAAa;IACtC,SAAkB,IAAI,KAAiB;CAwBxC;AAUD,YAAY,EAAC,oBAAoB,EAAC,CAAC;AACnC,cAAM,oBAAqB,SAAQ,aAAa;IAC9C,SAAkB,IAAI,KAA0B;CAoBjD;AAKD;;;;;;;;;;GAUG;AACH,YAAY,EAAC,SAAS,EAAC,CAAC;AACxB,cAAM,SAAU,SAAQ,aAAa;IACnC,SAAkB,IAAI,KAAc;gBAGlC,OAAO,EAAE,WAAW,EACpB,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE,aAAa,CAAC,MAAM,CAAC,EAC9B,MAAM,EAAE,cAAc,EACtB,OAAO,EAAE,aAAa,GAAG,SAAS;IA0EpC,WAAW,CAAC,KAAK,EAAE,KAAK;CAOzB;AAED,YAAY,EAAC,WAAW,EAAC,CAAC;AAC1B,cAAM,WAAY,YAAW,cAAc;IAkBhC,OAAO,EAAE,OAAO;IAjBzB,QAAQ,CAAC,IAAI,KAAgB;IAM7B,gBAAgB,EAAE,SAAS,CAAC;IAQ5B,OAAO,EAAE,aAAa,GAAG,SAAS,CAAC;gBAG1B,OAAO,EAAE,OAAO,EACvB,MAAM,EAAE,cAAc,EACtB,OAAO,EAAE,aAAa,GAAG,SAAS;IAOpC,IAAI,aAAa,YAEhB;IAED,UAAU,CAAC,KAAK,EAAE,OAAO,GAAG,IAAI;CASjC;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,IAAI;;;;;gCAv2CN,oBAAoB,QACvB,UAAU,KACf,CAAC,WAAW,EAAE,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC;;yBArVhB,OAAO;;;;;;;;CA2sDjC,CAAC;AAmBF;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,eAAO,MAAM,MAAM;YACV,OAAO,aACH,WAAW,GAAG,gBAAgB,YAC/B,aAAa,GACtB,QAAQ;iCArzDyB,gBAAgB;;;CA+1DnD,CAAC"}