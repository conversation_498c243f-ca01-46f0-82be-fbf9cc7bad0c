{"version": 3, "file": "errors.js", "sources": ["../../../src/tracing/errors.ts"], "sourcesContent": ["import {\n  addGlobalErrorInstrumentation<PERSON><PERSON><PERSON>,\n  addGlobalUnhandledRejectionInstrumentationHandler,\n  logger,\n} from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../debug-build';\nimport { getActiveSpan, getRootSpan } from '../utils/spanUtils';\nimport { SPAN_STATUS_ERROR } from './spanstatus';\n\nlet errorsInstrumented = false;\n\n/**  Only exposed for testing */\nexport function _resetErrorsInstrumented(): void {\n  errorsInstrumented = false;\n}\n\n/**\n * Ensure that global errors automatically set the active span status.\n */\nexport function registerSpanErrorInstrumentation(): void {\n  if (errorsInstrumented) {\n    return;\n  }\n\n  errorsInstrumented = true;\n  addGlobalErrorInstrumentationHandler(errorCallback);\n  addGlobalUnhandledRejectionInstrumentationHandler(errorCallback);\n}\n\n/**\n * If an error or unhandled promise occurs, we mark the active root span as failed\n */\nfunction errorCallback(): void {\n  const activeSpan = getActiveSpan();\n  const rootSpan = activeSpan && getRootSpan(activeSpan);\n  if (rootSpan) {\n    const message = 'internal_error';\n    DEBUG_BUILD && logger.log(`[Tracing] Root span: ${message} -> Global error occured`);\n    rootSpan.setStatus({ code: SPAN_STATUS_ERROR, message });\n  }\n}\n\n// The function name will be lost when bundling but we need to be able to identify this listener later to maintain the\n// node.js default exit behaviour\nerrorCallback.tag = 'sentry_tracingErrorCallback';\n"], "names": ["addGlobalErrorInstrumentationHandler", "addGlobalUnhandledRejectionInstrumentationHandler", "getActiveSpan", "getRootSpan", "DEBUG_BUILD", "logger", "SPAN_STATUS_ERROR"], "mappings": ";;;;;;;AAUA,IAAI,kBAAA,GAAqB,KAAK,CAAA;AAM9B;AACA;AACA;AACA;AACO,SAAS,gCAAgC,GAAS;AACzD,EAAE,IAAI,kBAAkB,EAAE;AAC1B,IAAI,OAAM;AACV,GAAE;AACF;AACA,EAAE,kBAAA,GAAqB,IAAI,CAAA;AAC3B,EAAEA,0CAAoC,CAAC,aAAa,CAAC,CAAA;AACrD,EAAEC,uDAAiD,CAAC,aAAa,CAAC,CAAA;AAClE,CAAA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,GAAS;AAC/B,EAAE,MAAM,UAAA,GAAaC,uBAAa,EAAE,CAAA;AACpC,EAAE,MAAM,WAAW,UAAA,IAAcC,qBAAW,CAAC,UAAU,CAAC,CAAA;AACxD,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,MAAM,OAAQ,GAAE,gBAAgB,CAAA;AACpC,IAAIC,sBAAY,IAAGC,YAAM,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,CAAC,wBAAwB,CAAC,CAAC,CAAA;AACxF,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEC,4BAAiB,EAAE,OAAQ,EAAC,CAAC,CAAA;AAC5D,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA,aAAa,CAAC,GAAI,GAAE,6BAA6B;;;;"}