<script>
export default {
  props: {
    url: { type: String, default: '' },
    thumb: { type: String, default: '' },
  },
  emits: ['error'],
  methods: {
    onImgError() {
      this.$emit('error');
    },
  },
};
</script>

<template>
  <a
    :href="url"
    target="_blank"
    rel="noreferrer noopener nofollow"
    class="image"
  >
    <div class="wrap">
      <img :src="thumb" alt="Picture message" @error="onImgError" />
    </div>
  </a>
</template>

<style lang="scss" scoped>
.image {
  display: block;

  .wrap {
    position: relative;
    display: flex;
    max-width: 100%;
  }

  img {
    width: 100%;
    max-width: 250px;
    border-radius: 8px;
  }
}
</style>
