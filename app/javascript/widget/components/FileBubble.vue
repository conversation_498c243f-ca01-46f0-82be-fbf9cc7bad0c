<script>
import FluentIcon from 'shared/components/FluentIcon/Index.vue';
import { getContrastingTextColor } from '@chatwoot/utils';

export default {
  components: {
    FluentIcon,
  },
  props: {
    url: {
      type: String,
      default: '',
    },
    isInProgress: {
      type: Boolean,
      default: false,
    },
    widgetColor: {
      type: String,
      default: '',
    },
    isUserBubble: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    textColor() {
      return this.isUserBubble && this.widgetColor
        ? getContrastingTextColor(this.widgetColor)
        : '';
    },
  },

};
</script>

<template>
  <a
    :href="url"
    target="_blank"
    rel="noreferrer noopener nofollow"
    class="file-link"
  >
    <div class="file flex flex-row items-center justify-center p-3 cursor-pointer">
      <div class="icon-wrap" :style="{ color: textColor }">
        <FluentIcon icon="document" size="32" />
      </div>
    </div>
  </a>
</template>

<style lang="scss" scoped>
.file-link {
  display: block;
  text-decoration: none;
}

.file {
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .icon-wrap {
    font-size: 2.5rem;
    color: var(--n-brand);
    line-height: 1;
  }
}
</style>
